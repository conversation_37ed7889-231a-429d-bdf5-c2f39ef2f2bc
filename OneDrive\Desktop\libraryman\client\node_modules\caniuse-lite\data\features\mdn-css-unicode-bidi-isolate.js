module.exports={A:{D:{"1":"0 pB qB rB sB tB uB vB wB xB yB zB QC 0B RC 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB I TC IC UC","2":"J UB K D E F A B C L M G","33":"1 2 3 4 5 6 7 8 9 N O P VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB"},L:{"1":"I"},B:{"1":"0 Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB I","2":"C L M G N O P"},C:{"1":"0 rB sB tB uB vB wB xB yB zB QC 0B RC 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC Q H R SC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB I TC IC UC tC uC","2":"sC PC J UB K D E F vC wC","33":"1 2 3 4 5 6 7 8 9 A B C L M G N O P VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB"},M:{"1":"IC"},A:{"2":"K D E F A B rC"},F:{"1":"0 cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC Q H R SC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z","2":"F B C 9C AD BD CD JC pC DD KC","33":"1 2 3 4 5 6 7 8 9 G N O P VB WB XB YB ZB aB bB"},K:{"1":"H","2":"A B C JC pC KC"},E:{"1":"B C L M G JC KC 2C 3C 4C XC YC LC 5C MC ZC aC bC cC dC 6C NC eC fC gC hC iC 7C OC jC kC lC mC nC oC","2":"J UB xC VC yC 8C","33":"K D E F A zC 0C 1C WC"},G:{"1":"ND OD PD QD RD SD TD UD VD WD XD XC YC LC YD MC ZC aC bC cC dC ZD NC eC fC gC hC iC aD OC jC kC lC mC nC oC","2":"VC ED qC FD","33":"E GD HD ID JD KD LD MD"},P:{"1":"1 2 3 4 5 6 7 8 9 iD jD kD lD mD WC nD oD pD qD rD MC NC OC sD","2":"J"},I:{"1":"I","2":"PC J cD dD eD fD qC gD hD"}},B:6,C:"isolate from unicode-bidi",D:undefined};
