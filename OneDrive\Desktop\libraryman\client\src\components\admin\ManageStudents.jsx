import React, { useState, useEffect } from 'react'
import { Plus, Upload, Download, Search, Edit, Trash2, Users } from 'lucide-react'
import axios from 'axios'

const ManageStudents = () => {
  const [students, setStudents] = useState([])
  const [colleges, setColleges] = useState([])
  const [departments, setDepartments] = useState([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [showBulkUpload, setShowBulkUpload] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCollege, setSelectedCollege] = useState('')
  const [selectedDepartment, setSelectedDepartment] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  const [formData, setFormData] = useState({
    user_id: '',
    name: '',
    email: '',
    college_id: '',
    department_id: '',
    designation: 'student',
    dob: '',
    validity_date: ''
  })

  const [bulkFile, setBulkFile] = useState(null)
  const [bulkCollegeId, setBulkCollegeId] = useState('')
  const [bulkDepartmentId, setBulkDepartmentId] = useState('')

  useEffect(() => {
    fetchStudents()
    fetchColleges()
  }, [currentPage, searchTerm])

  useEffect(() => {
    if (selectedCollege) {
      fetchDepartments(selectedCollege)
    }
  }, [selectedCollege])

  const fetchStudents = async () => {
    try {
      const response = await axios.get('/admin/users', {
        params: {
          role: 'student',
          page: currentPage,
          per_page: 10,
          search: searchTerm
        }
      })
      setStudents(response.data.users)
      setTotalPages(response.data.pagination.pages)
    } catch (error) {
      console.error('Failed to fetch students:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchColleges = async () => {
    try {
      const response = await axios.get('/admin/colleges')
      setColleges(response.data.colleges)
    } catch (error) {
      console.error('Failed to fetch colleges:', error)
    }
  }

  const fetchDepartments = async (collegeId) => {
    try {
      const response = await axios.get('/admin/departments', {
        params: { college_id: collegeId }
      })
      setDepartments(response.data.departments)
    } catch (error) {
      console.error('Failed to fetch departments:', error)
    }
  }

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      const response = await axios.post('/admin/users', formData)
      alert('Student added successfully!')
      setShowAddForm(false)
      setFormData({
        user_id: '',
        name: '',
        email: '',
        college_id: '',
        department_id: '',
        designation: 'student',
        dob: '',
        validity_date: ''
      })
      fetchStudents()
    } catch (error) {
      alert(error.response?.data?.error || 'Failed to add student')
    }
  }

  const handleBulkUpload = async (e) => {
    e.preventDefault()
    if (!bulkFile || !bulkCollegeId || !bulkDepartmentId) {
      alert('Please select file, college, and department')
      return
    }

    const formData = new FormData()
    formData.append('file', bulkFile)
    formData.append('college_id', bulkCollegeId)
    formData.append('department_id', bulkDepartmentId)

    try {
      const response = await axios.post('/admin/users/bulk', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      // Download credentials
      if (response.data.created_users.length > 0) {
        const credentialsResponse = await axios.post('/admin/users/credentials', {
          users: response.data.created_users
        }, {
          responseType: 'blob'
        })
        
        const url = window.URL.createObjectURL(new Blob([credentialsResponse.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', 'user_credentials.xlsx')
        document.body.appendChild(link)
        link.click()
        link.remove()
      }

      alert(`Successfully added ${response.data.created_users.length} students`)
      setShowBulkUpload(false)
      setBulkFile(null)
      setBulkCollegeId('')
      setBulkDepartmentId('')
      fetchStudents()
    } catch (error) {
      alert(error.response?.data?.error || 'Failed to upload students')
    }
  }

  if (loading) {
    return <div className="loading">Loading students...</div>
  }

  return (
    <div className="manage-students">
      <div className="page-header">
        <h1>Manage Students</h1>
        <div className="header-actions">
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddForm(true)}
          >
            <Plus size={16} />
            Add Student
          </button>
          <button 
            className="btn btn-secondary"
            onClick={() => setShowBulkUpload(true)}
          >
            <Upload size={16} />
            Bulk Upload
          </button>
        </div>
      </div>

      <div className="filters">
        <div className="search-box">
          <Search size={16} />
          <input
            type="text"
            placeholder="Search students..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="students-table">
        <table>
          <thead>
            <tr>
              <th>User ID</th>
              <th>Name</th>
              <th>Email</th>
              <th>College</th>
              <th>Department</th>
              <th>Validity Date</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {students.map((student) => (
              <tr key={student.id}>
                <td>{student.user_id}</td>
                <td>{student.name}</td>
                <td>{student.email}</td>
                <td>{student.college}</td>
                <td>{student.department}</td>
                <td>{new Date(student.validity_date).toLocaleDateString()}</td>
                <td>
                  <span className={`status ${student.is_active ? 'active' : 'inactive'}`}>
                    {student.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td>
                  <div className="actions">
                    <button className="btn-icon">
                      <Edit size={14} />
                    </button>
                    <button className="btn-icon danger">
                      <Trash2 size={14} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="pagination">
        <button 
          disabled={currentPage === 1}
          onClick={() => setCurrentPage(currentPage - 1)}
        >
          Previous
        </button>
        <span>Page {currentPage} of {totalPages}</span>
        <button 
          disabled={currentPage === totalPages}
          onClick={() => setCurrentPage(currentPage + 1)}
        >
          Next
        </button>
      </div>

      {/* Add Student Modal */}
      {showAddForm && (
        <div className="modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Add New Student</h2>
              <button onClick={() => setShowAddForm(false)}>×</button>
            </div>
            <form onSubmit={handleSubmit}>
              <div className="form-grid">
                <div className="form-group">
                  <label>User ID (Roll Number)</label>
                  <input
                    type="text"
                    name="user_id"
                    value={formData.user_id}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Name</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Email</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>College</label>
                  <select
                    name="college_id"
                    value={formData.college_id}
                    onChange={(e) => {
                      handleInputChange(e)
                      setSelectedCollege(e.target.value)
                    }}
                    required
                  >
                    <option value="">Select College</option>
                    {colleges.map((college) => (
                      <option key={college.id} value={college.id}>
                        {college.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Department</label>
                  <select
                    name="department_id"
                    value={formData.department_id}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select Department</option>
                    {departments.map((dept) => (
                      <option key={dept.id} value={dept.id}>
                        {dept.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="form-group">
                  <label>Date of Birth</label>
                  <input
                    type="date"
                    name="dob"
                    value={formData.dob}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label>Validity Date</label>
                  <input
                    type="date"
                    name="validity_date"
                    value={formData.validity_date}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              <div className="modal-actions">
                <button type="button" onClick={() => setShowAddForm(false)}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  Add Student
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Bulk Upload Modal */}
      {showBulkUpload && (
        <div className="modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Bulk Upload Students</h2>
              <button onClick={() => setShowBulkUpload(false)}>×</button>
            </div>
            <form onSubmit={handleBulkUpload}>
              <div className="form-group">
                <label>College</label>
                <select
                  value={bulkCollegeId}
                  onChange={(e) => {
                    setBulkCollegeId(e.target.value)
                    setSelectedCollege(e.target.value)
                  }}
                  required
                >
                  <option value="">Select College</option>
                  {colleges.map((college) => (
                    <option key={college.id} value={college.id}>
                      {college.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="form-group">
                <label>Department</label>
                <select
                  value={bulkDepartmentId}
                  onChange={(e) => setBulkDepartmentId(e.target.value)}
                  required
                >
                  <option value="">Select Department</option>
                  {departments.map((dept) => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="form-group">
                <label>Excel File</label>
                <input
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={(e) => setBulkFile(e.target.files[0])}
                  required
                />
                <small>
                  Excel file should contain columns: user_id, name, email, validity_date, dob
                </small>
              </div>
              <div className="modal-actions">
                <button type="button" onClick={() => setShowBulkUpload(false)}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  Upload Students
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default ManageStudents
