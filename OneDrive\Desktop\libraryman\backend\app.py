from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON>TManager
from flask_cors import CORS
from flask_migrate import Migrate
from dotenv import load_dotenv
import os
from apscheduler.schedulers.background import BackgroundScheduler
from datetime import datetime

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///library.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-string')

# Initialize extensions
db = SQLAlchemy(app)
jwt = JWTManager(app)
cors = CORS(app)
migrate = Migrate(app, db)

# Import models after db initialization
from models import User, Book, Ebook, College, Department, Circulation, NewsClipping

# Import routes
from routes.auth import auth_bp
from routes.admin import admin_bp
from routes.librarian import librarian_bp
from routes.student import student_bp

# Register blueprints
app.register_blueprint(auth_bp, url_prefix='/api/auth')
app.register_blueprint(admin_bp, url_prefix='/api/admin')
app.register_blueprint(librarian_bp, url_prefix='/api/librarian')
app.register_blueprint(student_bp, url_prefix='/api/student')

# Scheduler for validity date checks
scheduler = BackgroundScheduler()

def check_validity_dates():
    """Remove users whose validity date has expired"""
    with app.app_context():
        expired_users = User.query.filter(User.validity_date < datetime.now().date()).all()
        for user in expired_users:
            if user.role == 'student':  # Only remove students, not admin/librarian
                db.session.delete(user)
        db.session.commit()
        print(f"Removed {len(expired_users)} expired users")

# Schedule validity check to run daily at midnight
scheduler.add_job(func=check_validity_dates, trigger="cron", hour=0, minute=0)
scheduler.start()

@app.route('/')
def index():
    return {'message': 'Library Management System API'}

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
